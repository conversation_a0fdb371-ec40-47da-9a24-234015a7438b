import asyncio
import json
import logging
import random
import re
import time
from dataclasses import dataclass
from typing import Dict, List, Optional

from playwright.async_api import (
    async_playwright,
    Error as PlaywrightError,
    ViewportSize
)

# 로깅 설정
import coloredlogs

class ContextFilter(logging.Filter):
    def filter(self, record):
        record.proxy = getattr(record, 'proxy', 'N/A')
        record.device = getattr(record, 'device', 'N/A')
        return True

logger = logging.getLogger(__name__)
logger.addFilter(ContextFilter())

class ContextLogger:
    def __init__(self, logger, proxy=None, device=None, browser_id=None):
        self.logger = logger
        self.proxy = proxy
        self.device = device
        self.browser_id = browser_id

        # 브라우저별 색상 적용
        if browser_id == 1:
            self.device = f"🔵 {device}"  # 파란색 원
        elif browser_id == 2:
            self.device = f"🟢 {device}"  # 초록색 원
        else:
            self.device = device

    def log(self, level, msg, *args, **kwargs):
        extra = kwargs.pop('extra', {})
        extra.update({'proxy': self.proxy, 'device': self.device})
        self.logger.log(level, msg, *args, extra=extra, **kwargs)

    def info(self, msg, *args, **kwargs):
        self.log(logging.INFO, msg, *args, **kwargs)

    def warning(self, msg, *args, **kwargs):
        self.log(logging.WARNING, msg, *args, **kwargs)

    def error(self, msg, *args, **kwargs):
        self.log(logging.ERROR, msg, *args, **kwargs)

    def debug(self, msg, *args, **kwargs):
        self.log(logging.DEBUG, msg, *args, **kwargs)

coloredlogs.install(
    level=logging.INFO,
    fmt='📅 [%(asctime)s] 🌐 [Proxy: %(proxy)s] 📱 [Device: %(device)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    field_styles={
        'asctime': {'color': 'cyan'},
        'levelname': {'bold': True, 'color': 'black'},
        'proxy': {'color': 'blue'},
        'device': {'color': 'magenta'}
    },
    level_styles={
        'debug': {'color': 'green'},
        'info': {'color': 'white'},
        'warning': {'color': 'yellow'},
        'error': {'color': 'red'},
        'critical': {'color': 'red', 'bold': True}
    },
    handlers=[
        logging.FileHandler('naver_search.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

file_handler = next((h for h in logger.handlers if isinstance(h, logging.FileHandler)), None)
if not file_handler:
    file_handler = logging.FileHandler('naver_search.log', encoding='utf-8')
    logger.addHandler(file_handler)
file_handler.setFormatter(logging.Formatter(
    '[%(asctime)s] [Proxy: %(proxy)s] [Device: %(device)s] %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
))

@dataclass
class Config:
    PROXY_USERNAME: str = 'ogh3113'
    PROXY_PASSWORD: str = '@dlwnsdud0720'
    PROXY_FILE: str = "proxy.txt"
    UNFOUND_TARGETS_FILE: str = 'unfound_targets.json'
    NAVER_MOBILE_URL: str = "https://m.naver.com"
    SEARCH_INPUT_SELECTOR: str = '#MM_SEARCH_FAKE'
    MORE_RESULTS_BUTTON_SELECTOR: str = 'a.link_feed_more, a.group_more'
    PAGINATION_SELECTOR_TEMPLATE: str = 'a.pgn'
    PAGE_LOAD_TIMEOUT: int = 60000  # 60초로 최적화
    TARGET_PAGE_LOAD_TIMEOUT: int = 30000  # 목적 페이지 로딩 타임아웃
    MIN_DELAY: float = 0.5  # 최소 대기 시간 단축
    MAX_DELAY: float = 1.5  # 최대 대기 시간 단축
    SEARCH_DELAY_MIN: float = 1.5  # 검색 간 대기 시간 단축
    SEARCH_DELAY_MAX: float = 3.0
    PROXY_CHANGE_DELAY_MIN: float = 3.0  # 프록시 변경 대기 시간 단축
    PROXY_CHANGE_DELAY_MAX: float = 5.0
    MAX_PAGINATION_PAGES: int = 10
    MIN_SCROLL_AMOUNT: int = 100  # 스크롤 양 최적화
    MAX_SCROLL_AMOUNT: int = 400
    MIN_SCROLL_DURATION: float = 0.3  # 스크롤 시간 단축
    MAX_SCROLL_DURATION: float = 0.8
    # 목적 페이지 로딩 확인 설정
    TARGET_PAGE_CHECK_INTERVAL: float = 0.5  # 로딩 상태 확인 간격
    TARGET_PAGE_MAX_CHECKS: int = 60  # 최대 확인 횟수 (30초)
    NETWORK_IDLE_TIME: int = 500  # 네트워크 유휴 시간 (ms)

    # 브라우저 윈도우 배치 설정
    BROWSER1_POSITION_X: int = 50      # 브라우저1 X 좌표
    BROWSER1_POSITION_Y: int = 50      # 브라우저1 Y 좌표
    BROWSER2_POSITION_X: int = 900     # 브라우저2 X 좌표 (브라우저1 오른쪽)
    BROWSER2_POSITION_Y: int = 50      # 브라우저2 Y 좌표
    BROWSER_WIDTH: int = 800           # 브라우저 너비
    BROWSER_HEIGHT: int = 900          # 브라우저 높이

@dataclass
class DeviceConfig:
    user_agent: str
    viewport: ViewportSize
    description: str = ""

config = Config()

TARGET_URL_SELECTORS = [
    'div.api_txt_lines.total_tit a.link_tit',  # api_txt_lines total_tit 안의 제목 링크
]

DEVICE_CONFIGS = [
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Fold 5 (Unfolded)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Flip 5'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-F936N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Fold 4 (Unfolded)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-F721N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy Z Flip 4'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=393, height=852),
        description='iPhone 15'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=430, height=932),
        description='iPhone 15 Pro Max'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 14 Pro'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=428, height=926),
        description='iPhone 14 Plus'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.7 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=375, height=667),
        description='iPhone SE (3rd gen)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=414, height=896),
        description='iPhone 11'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=384, height=854),
        description='Android 14 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-S911N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=360, height=800),
        description='Android 13 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F946N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=412, height=915),
        description='Z Fold 5 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-F731N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 KAKAOTALK',
        viewport=ViewportSize(width=412, height=915),
        description='Z Flip 5 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 16 KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=428, height=926),
        description='iPhone 14 Plus KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 17_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1 KAKAOTALK',
        viewport=ViewportSize(width=430, height=932),
        description='iPhone 15 Pro Max KakaoTalk In-app'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 14; SM-S928N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        viewport=ViewportSize(width=412, height=915),
        description='Galaxy S24 Ultra (wider viewport)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 11; SM-G998N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.101 Mobile Safari/537.36',
        viewport=ViewportSize(width=384, height=854),
        description='Galaxy S21 Ultra (Android 11)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 10; SM-A515N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.127 Mobile Safari/537.36',
        viewport=ViewportSize(width=360, height=780),
        description='Galaxy A51 (Android 10)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (Linux; Android 13; SM-S901N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
        viewport=ViewportSize(width=360, height=800),
        description='Galaxy S23'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 13 Pro (iOS 15)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=375, height=812),
        description='iPhone 11 Pro (iOS 14)'
    ),
    DeviceConfig(
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
        viewport=ViewportSize(width=390, height=844),
        description='iPhone 14 Pro (iOS 16)'
    )
]

SEARCH_TARGETS = [
    {'keyword': '운암자이포레나', 'domain': 'aryatps.com'},
    {'keyword': '운암자이', 'domain': 'aryatps.com'},
    {'keyword': '일곡공원 위파크 1644-7240', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '일곡위파크', 'domain': 'immodelhouse9.quv.kr'},
    {'keyword': '장성 삼계 서현위드184', 'domain': 'damyangkoreaadelium.quv.kr'},
    {'keyword': '장성 바울루체', 'domain': 'immodelhouse10.quv.kr'},
    {'keyword': '대전 엘크루', 'domain': 'immodelhouse3.quv.kr'},
    {'keyword': '정읍 월드메르디앙', 'domain': 'modelhouse7l7.quv.kr'},
    {'keyword': '송암공원 중흥s클래스', 'domain': 'immodelhouse98.quv.kr'},
    {'keyword': '광주 중앙공원 롯데캐슬', 'domain': 'immodelhouse2.quv.kr'},
    {'keyword': '함평 미래프레지안', 'domain': 'modelhouse1d.quv.kr'},
    {'keyword': '봉선 이편한세상', 'domain': 'modelhouse1b.quv.kr'},
    {'keyword': '함평 서현 수와일 리버파크', 'domain': 'immodelhouse7.quv.kr'},
    {'keyword': '동림 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '동림2차 우방아이유쉘', 'domain': 'modelhouse2b.quv.kr'},
    {'keyword': '무등산자이앤어울림', 'domain': 'kingofyeosuhonors.quv.kr'},
    {'keyword': '광양 푸르지오 센터파크1644-7240', 'domain': 'modelhouse1g.quv.kr'},
    {'keyword': '광양 푸르지오 센터파크 모델하우스', 'domain': 'modelhouse1g.quv.kr'},
    {'keyword': '더샵광양레이크센텀', 'domain': 'immodelhouse1.quv.kr'},
    {'keyword': '상무 스위첸', 'domain': 'immodelhouse81.quv.kr'},
    {'keyword': '마포 빌리브디에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '마포빌리브에이블', 'domain': 'immodelhouse96.quv.kr'},
    {'keyword': '상무 모아미래도', 'domain': 'immodelhouse93.quv.kr'},
    {'keyword': '순천 더포레스트 마루힐', 'domain': 'immodelhouseb.quv.kr'},
    {'keyword': '무인카페 스타벅스', 'domain': 'starbuckskorea.quv.kr'},
    {'keyword': '중외공원 힐스테이트 공식1644-7240', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '힐스테이트 중외공원 공식', 'domain': 'immodelhome999.quv.kr'},
    {'keyword': '순천 지에이그린웰 예약', 'domain': 'immodelhouse90.quv.kr'},
    {'keyword': '선운2지구 예다음', 'domain': 'goldmodelhouse.quv.kr'},
    {'keyword': '아크로베스티뉴 공식', 'domain': 'inmodelhouse.quv.kr'},
    {'keyword': '광주 한양더힐 공식', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '각화 한양더힐1644-7240', 'domain': 'modelhouse1c.quv.kr'},
    {'keyword': '월산 힐스테이트1644 7240', 'domain': 'immodelhouse4.quv.kr'},
    {'keyword': '중앙공원 위파크', 'domain': 'immodelhouse99.quv.kr'},
    {'keyword': '화정 두산위브 모델하우스', 'domain': 'modelhouse1a.quv.kr'},
    {'keyword': '익산역 유탑유블레스', 'domain': 'modelhouse1e.quv.kr'},
    {'keyword': '진월 더리브 라포레', 'domain': 'iamodelhome.quv.kr'},
    {'keyword': '무등산 우방아이유쉘', 'domain': 'thesynergy.quv.kr'},
    {'keyword': '힐스테이트 천호역1644-7240', 'domain': 'immodelhouse91.quv.kr'}
]

async def load_proxies(file_path: str, context_logger=None) -> List[str]:
    log = context_logger if context_logger else logger
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            proxies = [line.strip() for line in f if line.strip()]
        log.info(f"{len(proxies)}개의 프록시를 '{file_path}'에서 로드했습니다.")
        return proxies
    except FileNotFoundError:
        log.error(f"오류: '{file_path}' 파일을 찾을 수 없습니다.")
        return []
    except Exception as e:
        log.error(f"프록시 로드 중 오류 발생: {e}")
        return []

async def human_like_delay(min_seconds: Optional[float] = None, max_seconds: Optional[float] = None, context_logger=None):
    if min_seconds is None:
        min_seconds = config.MIN_DELAY
    if max_seconds is None:
        max_seconds = config.MAX_DELAY
    delay_time = random.uniform(min_seconds, max_seconds)
    log = context_logger if context_logger else logger
    log.debug(f"'{delay_time:.2f}'초 동안 대기합니다.")
    await asyncio.sleep(delay_time)

async def random_scroll(page, context_logger=None):
    scroll_amount = random.randint(config.MIN_SCROLL_AMOUNT, config.MAX_SCROLL_AMOUNT)
    scroll_duration = random.uniform(config.MIN_SCROLL_DURATION, config.MAX_SCROLL_DURATION)
    log = context_logger if context_logger else logger
    log.debug(f"'{scroll_amount}' 픽셀만큼 스크롤하고 '{scroll_duration:.2f}'초 대기합니다.")
    await page.evaluate(f"window.scrollBy(0, {scroll_amount})")
    await asyncio.sleep(scroll_duration)



async def check_network_connection(page, context_logger=None) -> bool:
    """네트워크 연결 상태 확인 (빠른 테스트)"""
    log = context_logger if context_logger else logger
    try:
        # 빠른 네트워크 테스트
        response = await page.goto("https://www.google.com", timeout=10000, wait_until='domcontentloaded')
        if response and response.status == 200:
            log.debug("네트워크 연결 정상")
            return True
        else:
            log.warning("네트워크 연결 불안정")
            return False
    except Exception as e:
        log.warning(f"네트워크 연결 확인 실패: {e}")
        return False

async def wait_for_target_page_load(page, context_logger=None) -> bool:
    """목적 페이지 로딩 완료까지 대기하는 고급 알고리즘"""
    log = context_logger if context_logger else logger

    try:
        # 1단계: 기본 페이지 로드 대기
        log.debug("목적 페이지 로딩 대기 시작...")

        # 2단계: DOM 로딩 완료 대기
        await page.wait_for_load_state('domcontentloaded', timeout=config.TARGET_PAGE_LOAD_TIMEOUT)

        # 3단계: 네트워크 활동 안정화 대기
        try:
            await page.wait_for_load_state('networkidle', timeout=config.NETWORK_IDLE_TIME)
        except Exception:
            # 네트워크 유휴 상태를 기다리지 못해도 계속 진행
            pass

        # 4단계: 페이지 내용 로딩 확인
        checks_performed = 0
        while checks_performed < config.TARGET_PAGE_MAX_CHECKS:
            try:
                # 페이지 제목이 로드되었는지 확인
                title = await page.title()
                if title and len(title.strip()) > 0:
                    log.debug(f"페이지 제목 확인: {title[:50]}...")

                    # 페이지 내용이 실제로 로드되었는지 확인
                    body_content = await page.evaluate("() => document.body ? document.body.innerText.length : 0")
                    if body_content > 100:  # 최소한의 내용이 있는지 확인
                        log.debug(f"목적 페이지 로딩 완료 (내용 길이: {body_content})")
                        return True

                # 짧은 간격으로 재확인
                await asyncio.sleep(config.TARGET_PAGE_CHECK_INTERVAL)
                checks_performed += 1

            except Exception as e:
                log.debug(f"페이지 로딩 확인 중 오류: {e}")
                await asyncio.sleep(config.TARGET_PAGE_CHECK_INTERVAL)
                checks_performed += 1

        # 5단계: 최종 확인 실패 시에도 기본 대기 시간 제공
        log.warning("목적 페이지 로딩 확인 실패, 기본 대기 시간 적용")
        await asyncio.sleep(2)  # 최소 대기 시간
        return True  # 실패해도 계속 진행

    except Exception as e:
        log.warning(f"목적 페이지 로딩 대기 중 오류: {e}")
        await asyncio.sleep(1)  # 최소 대기 시간
        return True  # 오류가 있어도 계속 진행

async def smart_page_wait(page, action_description: str, context_logger=None) -> None:
    """스마트한 페이지 대기 (빠르고 확실한)"""
    log = context_logger if context_logger else logger

    # 빠른 기본 대기
    await asyncio.sleep(0.5)

    # 페이지 상태 확인
    try:
        await page.wait_for_load_state('domcontentloaded', timeout=5000)
    except Exception:
        pass  # 타임아웃이어도 계속 진행

    # 최소한의 스크롤
    await page.evaluate("window.scrollBy(0, 200)")
    await asyncio.sleep(0.3)

    log.debug(f"{action_description} - 이동 완료")

async def load_page_and_scroll(page, action_description: str, context_logger=None) -> None:
    """기존 호환성을 위한 래퍼 함수 (최적화됨)"""
    await smart_page_wait(page, action_description, context_logger)
    log = context_logger if context_logger else logger
    log.info(f"{action_description} - 이동 완료")

async def return_to_naver_home(page, context_logger) -> bool:
    """네이버 홈으로 이동 (최적화된 재시도 로직)"""
    max_retries = 2  # 재시도 횟수 줄임
    for attempt in range(max_retries):
        try:
            # 빠른 페이지 로드
            await page.goto(config.NAVER_MOBILE_URL, timeout=config.PAGE_LOAD_TIMEOUT, wait_until='domcontentloaded')

            # 검색 입력창 로드 확인 (빠른 확인)
            try:
                await page.wait_for_selector(config.SEARCH_INPUT_SELECTOR, timeout=5000)
                # 최소한의 안정화 시간
                await asyncio.sleep(0.5)
                context_logger.debug(f"네이버 홈 이동 성공 (시도 {attempt + 1}/{max_retries})")
                return True
            except Exception:
                context_logger.warning(f"검색창 로드 실패 (시도 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)  # 짧은 대기
                    continue

        except Exception as e:
            error_msg = str(e)
            if "Timeout" in error_msg:
                context_logger.warning(f"타임아웃 발생 (시도 {attempt + 1}/{max_retries})")
            elif "net::" in error_msg:
                context_logger.warning(f"네트워크 오류 (시도 {attempt + 1}/{max_retries})")
            else:
                context_logger.warning(f"네이버 홈 이동 실패 (시도 {attempt + 1}/{max_retries})")

            if attempt < max_retries - 1:
                wait_time = 5 + (attempt * 3)  # 빠른 재시도
                context_logger.debug(f"{wait_time}초 대기 후 재시도...")
                await asyncio.sleep(wait_time)
                continue
            else:
                context_logger.error("네이버 홈 이동 최종 실패: %s", error_msg)
                return False

    return False

async def search_and_visit_on_page(page, domain: str, page_description: str, keyword: str, context_logger=None) -> Optional[str]:
    """페이지에서 목표 URL 검색 및 방문"""
    log = context_logger if context_logger else logger
    try:
        result = await find_and_click_target_link(page, domain, context_logger)
        if result:
            target_url, _ = result
            log.info("✅ %s에서 성공: %s", page_description, keyword)
            return target_url
        return None
    except Exception as e:
        log.error("검색 중 오류: %s", str(e))
        return None

async def try_pagination_page(page, page_num: int, domain: str, keyword: str, context_logger=None) -> Optional[str]:
    """특정 페이지로 이동하여 목표 URL 검색 (최적화됨)"""
    log = context_logger if context_logger else logger

    # 페이지네이션 버튼 찾기 (빠른 검색)
    target_page_button = None
    for attempt in range(2):  # 재시도 횟수 줄임
        pagination_buttons = await page.query_selector_all(config.PAGINATION_SELECTOR_TEMPLATE)

        for button in pagination_buttons:
            try:
                text = await button.inner_text()
                if text.strip() == str(page_num):
                    target_page_button = button
                    break
            except Exception:
                continue

        if target_page_button:
            break

        if attempt < 1:
            await asyncio.sleep(0.5)  # 짧은 대기

    if not target_page_button:
        return None

    try:
        await target_page_button.click()
        await smart_page_wait(page, f"{page_num} 페이지", context_logger)
        return await search_and_visit_on_page(page, domain, f"{page_num} 페이지", keyword, context_logger)
    except Exception as e:
        log.warning("페이지 %d 처리 중 오류: %s", page_num, str(e))
        return None

async def find_and_click_target_link(page, domain: str, context_logger=None) -> Optional[tuple]:
    """목표 도메인을 포함한 링크를 찾아서 클릭 (최적화된 로딩 대기 포함)"""
    log = context_logger if context_logger else logger

    for selector in TARGET_URL_SELECTORS:
        try:
            links = await page.query_selector_all(selector)
            for link in links:
                try:
                    href = await link.get_attribute('href')
                    if href and domain in href:
                        log.debug(f"목표 링크 발견: {href}")

                        # 빠른 클릭 준비
                        await asyncio.sleep(0.5)

                        # 링크 클릭
                        await link.click()
                        log.debug("링크 클릭 완료, 목적 페이지 로딩 대기 시작")

                        # 목적 페이지 로딩 완료 대기
                        await wait_for_target_page_load(page, context_logger)

                        log.info(f"✅ 방문 성공: {href} (제목 링크)")
                        return href, "제목 링크"
                except Exception as e:
                    log.debug(f"링크 처리 중 오류: {e}")
                    continue
        except Exception as e:
            log.debug(f"셀렉터 처리 중 오류: {e}")
            continue
    return None

async def search_naver(page, keyword: str, domain: str, context_logger=None) -> Optional[str]:
    log = context_logger if context_logger else logger
    try:
        try:
            # 빠른 검색 실행
            await page.fill(config.SEARCH_INPUT_SELECTOR, keyword)
            await asyncio.sleep(0.5)  # 짧은 대기
            await page.press(config.SEARCH_INPUT_SELECTOR, 'Enter')

            # 검색 결과 로딩 대기 (최적화)
            await page.wait_for_load_state('domcontentloaded', timeout=15000)
            await asyncio.sleep(1)  # 최소 안정화 시간

            # 최소한의 스크롤
            await page.evaluate("window.scrollBy(0, 300)")
            await asyncio.sleep(0.5)

        except PlaywrightError as e:
            log.error(f"검색 실행 중 Playwright 오류 발생: {e}. 키워드: '{keyword}'")
            return None
        except Exception as e:
            log.error(f"검색 실행 중 예상치 못한 오류 발생: {e}. 키워드: '{keyword}'")
            return None

        target_url = await search_and_visit_on_page(page, domain, "메인 페이지", keyword, context_logger)
        if target_url:
            return target_url

        # 더보기 버튼 검색
        more_buttons = await page.query_selector_all(config.MORE_RESULTS_BUTTON_SELECTOR)
        valid_more_button = None

        for button in more_buttons:
            try:
                button_text = await button.inner_text()
                href = await button.get_attribute('href')
                if (button_text and "검색결과" in button_text and "더보기" in button_text and
                    href and not href.startswith('https://m.ad.search.naver.com/') and
                    not href.startswith('https://fin.land.naver.com/')):
                    valid_more_button = button
                    break
            except Exception:
                continue

        if valid_more_button:
            try:
                await valid_more_button.click()
                await smart_page_wait(page, "2페이지", context_logger)
                target_url = await search_and_visit_on_page(page, domain, "2페이지", keyword, context_logger)
                if target_url:
                    return target_url
            except Exception:
                pass
        else:
            # 페이지네이션으로 2페이지 이동 시도
            target_url = await try_pagination_page(page, 2, domain, keyword, context_logger)
            if target_url:
                return target_url

        # 3페이지부터 10페이지까지 순차 검색 (빠른 처리)
        await asyncio.sleep(0.5)  # 짧은 대기
        for i in range(3, config.MAX_PAGINATION_PAGES + 1):
            target_url = await try_pagination_page(page, i, domain, keyword, context_logger)
            if target_url:
                return target_url
            # 페이지 버튼이 없으면 더 이상 페이지가 없다는 의미
            if not await page.query_selector_all(config.PAGINATION_SELECTOR_TEMPLATE):
                break

        log.debug(f"모든 페이지 검색 완료, 목표 URL을 찾지 못함")
        return None
    except Exception as e:
        log.error(f"'{keyword}' 검색어와 도메인 '{domain}' 검색 중 치명적인 오류 발생: {e}")
        return None

def get_optimal_browser_positions() -> tuple:
    """화면 해상도에 따른 최적의 브라우저 배치 계산"""
    try:
        import tkinter as tk
        root = tk.Tk()
        screen_width = root.winfo_screenwidth()
        root.destroy()

        # 화면 크기에 따른 브라우저 배치 계산
        if screen_width >= 1920:  # Full HD 이상
            # 큰 화면: 브라우저를 나란히 배치
            browser1_x, browser1_y = 50, 50
            browser2_x, browser2_y = 950, 50
            browser_width, browser_height = 850, 950
        elif screen_width >= 1600:  # HD+
            # 중간 화면: 약간 겹치게 배치
            browser1_x, browser1_y = 30, 30
            browser2_x, browser2_y = 750, 30
            browser_width, browser_height = 750, 850
        else:  # HD 이하
            # 작은 화면: 세로로 배치
            browser1_x, browser1_y = 50, 50
            browser2_x, browser2_y = 50, 500
            browser_width, browser_height = 700, 450

        return (browser1_x, browser1_y, browser2_x, browser2_y, browser_width, browser_height)

    except Exception:
        # tkinter 사용 불가시 기본값 반환
        return (50, 50, 900, 50, 800, 900)

def save_unfound_targets(unfound_targets: List[Dict], file_path: str, context_logger=None) -> None:
    log = context_logger if context_logger else logger
    if unfound_targets:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(unfound_targets, f, ensure_ascii=False, indent=4)
            log.info(f"목표 URL을 찾지 못한 검색 설정 {len(unfound_targets)}개를 '{file_path}' 파일에 저장했습니다.")
        except Exception as e:
            log.error(f"찾지 못한 검색 설정 저장 중 오류 발생: {e}")
    else:
        log.info("목표 URL을 찾지 못한 검색 설정이 없습니다.")

async def process_browser_tasks(browser, targets: List[Dict], browser_id: int, proxy: str, device_config: DeviceConfig) -> List[Dict]:
    """단일 브라우저에서 할당된 검색 작업들을 처리"""
    unfound_targets = []
    user_agent = device_config.user_agent
    viewport = device_config.viewport
    description = device_config.description

    context_logger = ContextLogger(logger, proxy=proxy, device=f"{description} (브라우저 {browser_id})", browser_id=browser_id)
    context_logger.info("🚀 시작 - %d개 작업", len(targets))

    try:
        context = await browser.new_context(
            user_agent=user_agent,
            viewport=viewport,
            locale='ko-KR',
            permissions=['geolocation'],
            ignore_https_errors=True
        )
        page = await context.new_page()

        # 브라우저 창 제목 및 시각적 구분 설정
        try:
            browser_color = "🔵" if browser_id == 1 else "🟢"
            await page.evaluate(f"""
                document.title = '{browser_color} 네이버 검색 봇 - 브라우저 {browser_id} | {description}';

                // 브라우저 구분을 위한 상단 바 추가
                const headerBar = document.createElement('div');
                headerBar.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 5px;
                    background: {'linear-gradient(90deg, #0066cc, #0099ff)' if browser_id == 1 else 'linear-gradient(90deg, #00cc66, #00ff99)'};
                    z-index: 10000;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                `;
                document.body.appendChild(headerBar);
            """)
        except Exception:
            pass  # 스타일 설정 실패해도 계속 진행

        # 빠른 네트워크 연결 상태 확인 (선택적)
        # 네트워크 확인을 건너뛰고 바로 네이버로 이동하여 시간 절약

        # 초기 네이버 홈 로드 (재시도 로직 사용)
        if not await return_to_naver_home(page, context_logger):
            context_logger.error("초기 네이버 홈 로드 실패")
            return targets  # 모든 타겟을 unfound로 반환

        # 네이버 페이지 로드 상태 추적
        is_on_naver_home = True

        for i, target in enumerate(targets):
            keyword = target.get('keyword', '')
            domain = target.get('domain', '')
            if not keyword or not domain:
                continue

            context_logger.info("🔍 %s (%d/%d)", keyword, i+1, len(targets))

            # 네이버 홈에 있지 않은 경우에만 이동
            if not is_on_naver_home:
                if not await return_to_naver_home(page, context_logger):
                    unfound_targets.append(target)
                    continue
                is_on_naver_home = True

            found = await search_naver(page, keyword, domain, context_logger)
            is_on_naver_home = False

            if found:
                # 다음 검색을 위해 네이버 홈으로 이동
                if not await return_to_naver_home(page, context_logger):
                    break
                is_on_naver_home = True
            else:
                context_logger.warning("❌ 실패: %s", keyword)
                unfound_targets.append(target)
                # 다음 검색을 위해 네이버 홈으로 이동
                if not await return_to_naver_home(page, context_logger):
                    break
                is_on_naver_home = True

            await human_like_delay(config.SEARCH_DELAY_MIN, config.SEARCH_DELAY_MAX, context_logger)

        await context.close()
        context_logger.info(f"브라우저 {browser_id} 작업 완료")

    except Exception as e:
        context_logger.error(f"브라우저 {browser_id} 처리 중 오류 발생: {e}")
        unfound_targets.extend(targets)

    return unfound_targets

async def main() -> None:
    # 초기 프록시 로드는 전역 logger 사용 (아직 프록시 정보가 없음)
    proxies = await load_proxies(config.PROXY_FILE)
    if not proxies:
        logger.error("프록시 목록이 비어 있습니다. 프로그램을 종료합니다.")
        return

    unfound_targets = []
    async with async_playwright() as p:
        for proxy in proxies:
            from playwright.async_api import ProxySettings
            proxy_config = ProxySettings(
                server=proxy,
                username=config.PROXY_USERNAME,
                password=config.PROXY_PASSWORD
            )

            # 동일한 디바이스 설정 선택
            random_device_raw = random.choice(DEVICE_CONFIGS)
            if isinstance(random_device_raw, dict):
                viewport_data = random_device_raw.get('viewport', {'width': 0, 'height': 0})
                device_config = DeviceConfig(
                    user_agent=random_device_raw.get('user_agent', ''),
                    viewport=ViewportSize(**viewport_data),
                    description=random_device_raw.get('description', 'Unknown Device')
                )
            else:
                device_config = random_device_raw

            context_logger = ContextLogger(logger, proxy=proxy, device=device_config.description)
            context_logger.info(f"프록시 시작: {proxy}")
            context_logger.info(f"디바이스 설정: {device_config.description} - User-Agent='{device_config.user_agent}', Viewport='{device_config.viewport}'")

            # 검색 대상을 2개로 분할
            search_targets_shuffled = SEARCH_TARGETS.copy()
            random.shuffle(search_targets_shuffled)

            mid_point = len(search_targets_shuffled) // 2
            targets_browser1 = search_targets_shuffled[:mid_point]
            targets_browser2 = search_targets_shuffled[mid_point:]

            context_logger.info(f"작업 분할: 브라우저1({len(targets_browser1)}개), 브라우저2({len(targets_browser2)}개)")

            # 화면 해상도에 따른 최적 브라우저 배치 계산
            browser1_x, browser1_y, browser2_x, browser2_y, browser_width, browser_height = get_optimal_browser_positions()
            context_logger.info(f"브라우저 배치: 브라우저1({browser1_x},{browser1_y}) 브라우저2({browser2_x},{browser2_y}) 크기({browser_width}x{browser_height})")

            browser1 = None
            browser2 = None
            try:
                # 2개의 브라우저 동시 실행 (최적화된 설정 + 윈도우 위치 설정)
                base_browser_args = [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-background-networking',
                    '--aggressive-cache-discard',
                    '--memory-pressure-off'
                ]

                # 브라우저 1: 계산된 위치에 배치
                browser1_args = base_browser_args + [
                    f'--window-position={browser1_x},{browser1_y}',
                    f'--window-size={browser_width},{browser_height}'
                ]

                # 브라우저 2: 계산된 위치에 배치
                browser2_args = base_browser_args + [
                    f'--window-position={browser2_x},{browser2_y}',
                    f'--window-size={browser_width},{browser_height}'
                ]

                browser1 = await p.chromium.launch(
                    headless=False,
                    proxy=proxy_config,
                    args=browser1_args
                )

                browser2 = await p.chromium.launch(
                    headless=False,
                    proxy=proxy_config,
                    args=browser2_args
                )

                # 2개의 브라우저에서 병렬로 작업 실행
                tasks = [
                    process_browser_tasks(browser1, targets_browser1, 1, proxy, device_config),
                    process_browser_tasks(browser2, targets_browser2, 2, proxy, device_config)
                ]

                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 결과 수집
                for result in results:
                    if isinstance(result, Exception):
                        context_logger.error(f"브라우저 작업 중 예외 발생: {result}")
                    elif isinstance(result, list):
                        unfound_targets.extend(result)

            finally:
                if browser1:
                    await browser1.close()
                if browser2:
                    await browser2.close()

            context_logger.info(f"프록시 {proxy} 작업 완료")
            await human_like_delay(config.PROXY_CHANGE_DELAY_MIN, config.PROXY_CHANGE_DELAY_MAX)

    save_unfound_targets(unfound_targets, config.UNFOUND_TARGETS_FILE)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("사용자에 의해 프로그램이 중단되었습니다.")
    except Exception as e:
        logger.error(f"프로그램 실행 중 예상치 못한 오류 발생: {e}")
    finally:
        logger.info("프로그램 종료.")
